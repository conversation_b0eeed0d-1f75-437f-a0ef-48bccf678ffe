package main

import (
	"crypto/tls"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"securebooks/handlers"
	"securebooks/utils"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	_ "github.com/lib/pq" // PostgreSQL driver
)

// DBMiddleware injects the database connection into the Gin context
func DBMiddleware(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		fmt.Printf("DBMiddleware: setting db in context for path: %s\n", c.Request.URL.Path)
		c.Set("db", db)
		c.Next()
	}
}

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Debugging credentials
	log.Println("DB_USER:", os.Getenv("DB_USER"))
	log.Println("DB_PASSWORD:", os.Getenv("DB_PASSWORD"))

	// Initialize database connection
	if err := InitDB(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Create Gin router
	router := gin.Default()
	router.Use(DBMiddleware(DB))

	// Set Gin mode based on environment
	if os.Getenv("GIN_MODE") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// CORS configuration (allow localhost during development)
	router.Use(cors.New(cors.Config{
		AllowOriginFunc: func(origin string) bool {
			return strings.HasPrefix(origin, "http://localhost")
		},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// API routes
	api := router.Group("/api/v1")
	{
		// Auth routes
		auth := api.Group("/auth")
		{
			auth.POST("/register", handlers.RegisterHandler)
			auth.POST("/login", handlers.LoginHandler)
			auth.POST("/logout", handlers.LogoutHandler)
			auth.POST("/refresh", handlers.RefreshTokenHandler)
			auth.POST("/reset-password", handlers.ResetPasswordHandler)
			auth.POST("/reset-password/confirm", handlers.ResetPasswordConfirmHandler)
			auth.DELETE("/delete", utils.AuthMiddleware(), handlers.DeleteAccountHandler)
		}

		// Protected routes
		protected := api.Group("/")
		protected.Use(utils.AuthMiddleware())
		{
			// Transaction routes
			transactions := protected.Group("/transactions")
			{
				transactions.GET("/", handlers.GetTransactionsHandler)
				transactions.POST("/", handlers.CreateTransactionHandler)
				transactions.GET("/:id", handlers.GetTransactionHandler)
				transactions.PUT("/:id", handlers.UpdateTransactionHandler)
				transactions.DELETE("/:id", handlers.DeleteTransactionHandler)
				transactions.GET("/export/csv", handlers.ExportTransactionsCSVHandler)
			}

			// Recurring transactions
			recurring := protected.Group("/recurring")
			{
				recurring.GET("/", handlers.GetRecurringTransactionsHandler)
				recurring.POST("/", handlers.CreateRecurringTransactionHandler)
				recurring.GET("/:id", handlers.GetRecurringTransactionHandler)
				recurring.PUT("/:id", handlers.UpdateRecurringTransactionHandler)
				recurring.DELETE("/:id", handlers.DeleteRecurringTransactionHandler)
			}

			// Invoice routes
			invoices := protected.Group("/invoices")
			{
				invoices.GET("/", handlers.GetInvoicesHandler)
				invoices.POST("/", handlers.CreateInvoiceHandler)
				invoices.GET("/:id", handlers.GetInvoiceHandler)
				invoices.PUT("/:id", handlers.UpdateInvoiceHandler)
				invoices.DELETE("/:id", handlers.DeleteInvoiceHandler)
				invoices.POST("/:id/send", handlers.SendInvoiceHandler)
				invoices.GET("/:id/pdf", handlers.GenerateInvoicePDFHandler)
			}

			// Templates
			templates := protected.Group("/templates")
			{
				templates.GET("/", handlers.GetInvoiceTemplatesHandler)
				templates.POST("/", handlers.CreateInvoiceTemplateHandler)
				templates.GET("/:id", handlers.GetInvoiceTemplateHandler)
				templates.PUT("/:id", handlers.UpdateInvoiceTemplateHandler)
				templates.DELETE("/:id", handlers.DeleteInvoiceTemplateHandler)
			}

			// Analytics
			analytics := protected.Group("/analytics")
			{
				analytics.GET("/summary", handlers.GetSummaryHandler)
				analytics.GET("/category-breakdown", handlers.GetCategoryBreakdownHandler)
				analytics.GET("/monthly-trends", handlers.GetMonthlyTrendsHandler)
				analytics.GET("/spending-patterns", handlers.SpendingPatternsHandler)
				analytics.GET("/forecast", handlers.ForecastHandler)
				analytics.GET("/anomalies", handlers.AnomaliesHandler)
				analytics.GET("/health-score", handlers.HealthScoreHandler)
				analytics.GET("/savings", handlers.SavingsHandler)
			}

			// Sync routes
			sync := protected.Group("/sync")
			{
				sync.POST("/transactions", handlers.SyncTransactionsHandler)
				sync.POST("/recurring", handlers.SyncRecurringTransactionsHandler)
				sync.POST("/invoices", handlers.SyncInvoicesHandler)
				sync.POST("/templates", handlers.SyncInvoiceTemplatesHandler)
			}

			// PDF routes
			pdf := protected.Group("/pdf")
			{
				pdf.POST("/invoice", handlers.GenerateInvoicePDFHandler)
			}

			// Budget routes
			budgets := protected.Group("/budgets")
			{
				budgets.POST("/", handlers.CreateBudgetHandler)
				budgets.GET("/", handlers.ListBudgetsHandler)
				budgets.PUT("/:id", handlers.UpdateBudgetHandler)
				budgets.DELETE("/:id", handlers.DeleteBudgetHandler)
				budgets.GET("/status", handlers.GetBudgetStatusHandler)
			}
		}
	}

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "SecureBooks API is running",
		})
	})

	// Background job: clean old drafts
	go func() {
		for {
			draftDays := utils.GetDraftExpirationDays()
			log.Printf("[Draft Cleanup] Expiration: %d days", draftDays)

			dbURL := os.Getenv("DATABASE_URL")
			if dbURL == "" {
				log.Printf("[Draft Cleanup] DATABASE_URL not set")
				time.Sleep(24 * time.Hour)
				continue
			}

			db, err := sql.Open("postgres", dbURL)
			if err != nil {
				log.Printf("[Draft Cleanup] DB connection error: %v", err)
				time.Sleep(24 * time.Hour)
				continue
			}

			res, err := db.Exec(`DELETE FROM invoices WHERE (status = 'draft' OR status = 'temporary') AND date < NOW() - INTERVAL '` + strconv.Itoa(draftDays) + ` days'`)
			if err != nil {
				log.Printf("[Draft Cleanup] Error deleting drafts: %v", err)
			} else {
				count, _ := res.RowsAffected()
				log.Printf("[Draft Cleanup] Deleted %d old drafts", count)
			}
			db.Close()
			time.Sleep(24 * time.Hour)
		}
	}()

	// TLS certs (inside container from Docker volume)
	certFile := "/etc/letsencrypt/securebooks.duckdns.org_ecc/fullchain.cer"
	keyFile := "/etc/letsencrypt/securebooks.duckdns.org_ecc/securebooks.duckdns.org.key"

	// Load TLS certificate
	cert, err := tls.LoadX509KeyPair(certFile, keyFile)
	if err != nil {
		log.Fatal("Failed to load TLS certificate:", err)
	}

	// TLS configuration
	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		MinVersion:   tls.VersionTLS12,
		CurvePreferences: []tls.CurveID{
			tls.CurveP256,
			tls.X25519,
		},
	}

	// HTTP -> HTTPS redirect server (runs in background)
	go func() {
		log.Println("Starting HTTP -> HTTPS redirect server on :80")
		if err := http.ListenAndServe(":80", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			target := "https://" + r.Host + r.URL.String()
			http.Redirect(w, r, target, http.StatusMovedPermanently)
		})); err != nil {
			log.Fatal("HTTP redirect server failed:", err)
		}
	}()

	// Main HTTPS server
	server := &http.Server{
		Addr:      "0.0.0.0:443",
		Handler:   router,
		TLSConfig: tlsConfig,
	}

	log.Println("Server starting on port 443 with HTTPS")
	if err := server.ListenAndServeTLS("", ""); err != nil {
		log.Fatal("Failed to start TLS server:", err)
	}
}
