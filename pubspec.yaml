name: securebooks
description: A comprehensive SecureBooks app with expense tracking and invoicing features.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  
  # HTTP Client
  http: ^1.1.0
  
  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # UI Components
  fl_chart: ^0.65.0
  flutter_slidable: ^3.0.1
  intl: ^0.18.1
  
  # PDF Generation
  pdf: ^3.10.7
  path_provider: ^2.1.1
  
  # File Operations
  file_picker: ^6.1.1
  
  # Notifications
  flutter_local_notifications: ^16.3.0
  
  # Icons
  cupertino_icons: ^1.0.2
  connectivity_plus: ^5.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true